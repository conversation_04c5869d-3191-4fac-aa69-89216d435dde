const express = require('express');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { initializeDatabase } = require('./database');
const UserDao = require('./dao/userDao');
const app = express();

app.use(cors());
app.use(express.json());

// 静态文件服务 - 提供上传的图片
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 添加请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toLocaleString('zh-CN');
  console.log(`[${timestamp}] ${req.method} ${req.url}`);
  if (req.method === 'POST' && req.body) {
    console.log('请求数据:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/photos';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB限制
  },
  fileFilter: function (req, file, cb) {
    console.log('🔍 文件过滤器检查:');
    console.log('- 文件名:', file.originalname);
    console.log('- MIME类型:', file.mimetype);
    console.log('- 字段名:', file.fieldname);

    // 检查MIME类型或文件扩展名
    const isImage = file.mimetype.startsWith('image/') ||
                   /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(file.originalname);

    if (isImage) {
      console.log('✅ 文件类型验证通过');
      cb(null, true);
    } else {
      console.log('❌ 文件类型验证失败');
      cb(new Error('只允许上传图片文件'), false);
    }
  }
});

// 问卷提交接口
app.post('/api/survey/submit', (req, res) => {
  const submission = req.body;
  console.log('📝 收到新的问卷提交:');
  console.log('- 兴趣爱好:', submission.interests);
  console.log('- 饮食偏好:', submission.diets);
  console.log('- 健康状况:', submission.health);
  console.log('- 期望体验:', submission.expect);
  console.log('- 性别:', submission.gender);
  console.log('- 年龄组:', submission.ageGroup);
  console.log('- 月收入:', submission.monthlyIncome);
  console.log('- 文化认同:', submission.culturalIdentity);
  console.log('- 心理特征:', submission.psychologicalTraits);
  console.log('- 旅行频率:', submission.travelFrequency);
  console.log('- 建议:', submission.suggestion);

  let submissions = [];
  try {
    submissions = JSON.parse(fs.readFileSync('survey_submissions.json'));
  } catch (e) {}

  // 添加提交时间戳
  submission.submittedAt = new Date().toISOString();
  submissions.push(submission);

  fs.writeFileSync('survey_submissions.json', JSON.stringify(submissions, null, 2));
  console.log(`✅ 问卷已保存，当前总数: ${submissions.length}`);

  res.json({ success: true, message: '问卷提交成功' });
});

// 问卷统计接口
app.get('/api/survey/stats', (req, res) => {
  let submissions = [];
  try {
    submissions = JSON.parse(fs.readFileSync('survey_submissions.json'));
  } catch (e) {}

  console.log(`📊 生成问卷统计，共 ${submissions.length} 份提交`);

  // 统计各项数据
  const interest = {};
  const diets = {};
  const expect = {};
  const gender = {};
  const ageGroup = {};
  const monthlyIncome = {};
  const culturalIdentity = {};
  const psychologicalTraits = {};
  const travelFrequency = {};

  submissions.forEach(s => {
    // 兴趣爱好
    (s.interests || []).forEach(i => interest[i] = (interest[i] || 0) + 1);

    // 饮食偏好
    (s.diets || []).forEach(d => diets[d] = (diets[d] || 0) + 1);

    // 期望体验
    if (s.expect) expect[s.expect] = (expect[s.expect] || 0) + 1;

    // 性别
    if (s.gender) gender[s.gender] = (gender[s.gender] || 0) + 1;

    // 年龄组
    if (s.ageGroup) ageGroup[s.ageGroup] = (ageGroup[s.ageGroup] || 0) + 1;

    // 月收入
    if (s.monthlyIncome) monthlyIncome[s.monthlyIncome] = (monthlyIncome[s.monthlyIncome] || 0) + 1;

    // 文化认同
    if (s.culturalIdentity) culturalIdentity[s.culturalIdentity] = (culturalIdentity[s.culturalIdentity] || 0) + 1;

    // 心理特征
    (s.psychologicalTraits || []).forEach(p => psychologicalTraits[p] = (psychologicalTraits[p] || 0) + 1);

    // 旅行频率
    if (s.travelFrequency) travelFrequency[s.travelFrequency] = (travelFrequency[s.travelFrequency] || 0) + 1;
  });

  const stats = {
    total: submissions.length,
    interest,
    diets,
    expect,
    gender,
    ageGroup,
    monthlyIncome,
    culturalIdentity,
    psychologicalTraits,
    travelFrequency
  };

  console.log('📈 统计结果:', JSON.stringify(stats, null, 2));
  res.json(stats);
});

// 反馈统计接口
app.get('/api/feedback/stats', (req, res) => {
  const data = JSON.parse(fs.readFileSync('data.json'));
  res.json(data.feedback);
});

// 照片上传接口
app.post('/api/photos/upload', upload.array('photos', 10), (req, res) => {
  try {
    console.log('📸 收到照片上传请求:');
    console.log('- 文件数量:', req.files?.length || 0);
    console.log('- 景点名称:', req.body.spotName);
    console.log('- 上传者:', req.body.uploader);
    console.log('- 用户角色:', req.body.userRole);
    console.log('- 标题:', req.body.title);
    console.log('- 描述:', req.body.description);

    const uploadedPhotos = [];
    const userRole = req.body.userRole || 'tourist';
    const spotName = req.body.spotName || '未知景点';
    
    req.files.forEach(file => {
      const photoData = {
        id: Date.now() + Math.random().toString(36).substr(2, 9),
        filename: file.filename,
        originalName: file.originalname,
        path: `/uploads/photos/${file.filename}`,
        spotName: spotName,
        uploader: req.body.uploader || 'anonymous',
        userRole: userRole,
        uploadTime: new Date().toISOString(),
        status: userRole === 'guide' ? 'approved' : 'pending', // 导游上传直接审核通过
        title: req.body.title || file.originalname,
        description: req.body.description || ''
      };
      
      uploadedPhotos.push(photoData);
    });
    
    // 保存照片信息到文件
    let photos = [];
    try {
      photos = JSON.parse(fs.readFileSync('photos.json'));
    } catch (e) {}
    
    photos.push(...uploadedPhotos);
    fs.writeFileSync('photos.json', JSON.stringify(photos, null, 2));

    console.log(`✅ 照片上传成功! 上传了 ${uploadedPhotos.length} 张照片`);
    uploadedPhotos.forEach((photo, index) => {
      console.log(`   ${index + 1}. ${photo.originalName} -> ${photo.filename}`);
    });
    console.log(`📊 照片库总数: ${photos.length}`);

    res.json({
      success: true,
      message: '照片上传成功',
      photos: uploadedPhotos
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '照片上传失败',
      error: error.message
    });
  }
});

// 获取照片列表接口
app.get('/api/photos', (req, res) => {
  try {
    let photos = [];
    try {
      photos = JSON.parse(fs.readFileSync('photos.json'));
    } catch (e) {}
    
    const { status, spotName, page = 1, limit = 20 } = req.query;
    
    // 过滤照片
    let filteredPhotos = photos;
    if (status) {
      filteredPhotos = filteredPhotos.filter(photo => photo.status === status);
    }
    if (spotName) {
      filteredPhotos = filteredPhotos.filter(photo => photo.spotName === spotName);
    }
    
    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedPhotos = filteredPhotos.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      photos: paginatedPhotos,
      total: filteredPhotos.length,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(filteredPhotos.length / limit)
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取照片列表失败',
      error: error.message
    });
  }
});

// 照片审核接口
app.post('/api/photos/:photoId/review', (req, res) => {
  try {
    const { photoId } = req.params;
    const { status, reason } = req.body;
    
    let photos = [];
    try {
      photos = JSON.parse(fs.readFileSync('photos.json'));
    } catch (e) {}
    
    const photoIndex = photos.findIndex(photo => photo.id === photoId);
    if (photoIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '照片不存在'
      });
    }
    
    photos[photoIndex].status = status;
    photos[photoIndex].reviewTime = new Date().toISOString();
    photos[photoIndex].reviewReason = reason;
    
    fs.writeFileSync('photos.json', JSON.stringify(photos, null, 2));
    
    res.json({
      success: true,
      message: '审核完成',
      photo: photos[photoIndex]
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '审核失败',
      error: error.message
    });
  }
});

// 删除照片接口
app.delete('/api/photos/:photoId', (req, res) => {
  try {
    const { photoId } = req.params;
    
    let photos = [];
    try {
      photos = JSON.parse(fs.readFileSync('photos.json'));
    } catch (e) {}
    
    const photoIndex = photos.findIndex(photo => photo.id === photoId);
    if (photoIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '照片不存在'
      });
    }
    
    const photo = photos[photoIndex];
    
    // 删除文件
    try {
      const filePath = path.join(__dirname, photo.path);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (e) {
      console.log('文件删除失败:', e.message);
    }
    
    // 从列表中移除
    photos.splice(photoIndex, 1);
    fs.writeFileSync('photos.json', JSON.stringify(photos, null, 2));
    
    res.json({
      success: true,
      message: '照片删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除失败',
      error: error.message
    });
  }
});

// 照片统计接口
app.get('/api/photos/stats', (req, res) => {
  try {
    let photos = [];
    try {
      photos = JSON.parse(fs.readFileSync('photos.json'));
    } catch (e) {}
    
    const stats = {
      total: photos.length,
      pending: photos.filter(p => p.status === 'pending').length,
      approved: photos.filter(p => p.status === 'approved').length,
      rejected: photos.filter(p => p.status === 'rejected').length,
      bySpot: {},
      byUploader: {}
    };
    
    // 按景点统计
    photos.forEach(photo => {
      stats.bySpot[photo.spotName] = (stats.bySpot[photo.spotName] || 0) + 1;
      stats.byUploader[photo.uploader] = (stats.byUploader[photo.uploader] || 0) + 1;
    });
    
    res.json({
      success: true,
      stats: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取统计失败',
      error: error.message
    });
  }
});

// 用户注册接口
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, email, password, role = 'tourist' } = req.body;

    console.log('👤 收到用户注册请求:');
    console.log('- 用户名:', username);
    console.log('- 邮箱:', email);
    console.log('- 角色:', role);

    // 检查用户名是否已存在
    const usernameExists = await UserDao.checkUsernameExists(username);
    if (usernameExists) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }

    // 检查邮箱是否已存在
    const emailExists = await UserDao.checkEmailExists(email);
    if (emailExists) {
      return res.status(400).json({
        success: false,
        message: '邮箱已存在'
      });
    }

    // 创建新用户
    const newUser = await UserDao.createUser({
      username,
      email,
      password, // 实际项目中应该加密密码
      role
    });

    console.log(`✅ 用户注册成功! 用户ID: ${newUser.id}`);

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = newUser;
    const responseUser = {
      id: userWithoutPassword.id.toString(),
      username: userWithoutPassword.username,
      email: userWithoutPassword.email,
      avatar: userWithoutPassword.avatar,
      role: userWithoutPassword.role,
      isActive: Boolean(userWithoutPassword.is_active),
      createdAt: userWithoutPassword.created_at ? new Date(userWithoutPassword.created_at + 'Z').toISOString() : new Date().toISOString()
    };

    console.log('📤 返回用户数据:', responseUser);

    res.json({
      success: true,
      message: '注册成功',
      user: responseUser
    });

  } catch (error) {
    console.error('❌ 用户注册失败:', error);
    res.status(500).json({
      success: false,
      message: '注册失败'
    });
  }
});

// 用户登录接口
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    console.log('🔐 收到用户登录请求:');
    console.log('- 用户名:', username);

    // 查找用户
    const user = await UserDao.findUserByCredentials(username, password);

    if (!user) {
      console.log('❌ 登录失败: 用户名或密码错误');
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    if (!user.is_active) {
      console.log('❌ 登录失败: 用户已被禁用');
      return res.status(401).json({
        success: false,
        message: '用户已被禁用'
      });
    }

    console.log(`✅ 用户登录成功! 用户: ${user.username}, 角色: ${user.role}`);

    // 返回用户信息
    const responseUser = {
      id: user.id.toString(),
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      role: user.role,
      isActive: Boolean(user.is_active),
      createdAt: user.created_at ? new Date(user.created_at + 'Z').toISOString() : new Date().toISOString()
    };

    console.log('📤 返回用户数据:', responseUser);

    res.json({
      success: true,
      message: '登录成功',
      user: responseUser
    });

  } catch (error) {
    console.error('❌ 用户登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 获取用户列表接口（仅导游可访问）
app.get('/api/users', async (req, res) => {
  try {
    console.log('👥 获取用户列表请求');

    const users = await UserDao.getAllUsers();

    // 格式化用户数据
    const formattedUsers = users.map(user => ({
      id: user.id.toString(),
      username: user.username,
      email: user.email,
      avatar: user.avatar,
      role: user.role,
      isActive: Boolean(user.is_active),
      createdAt: user.created_at ? new Date(user.created_at + 'Z').toISOString() : new Date().toISOString()
    }));

    console.log(`📊 返回用户列表，共 ${formattedUsers.length} 个用户`);

    res.json({
      success: true,
      users: formattedUsers,
      total: formattedUsers.length
    });

  } catch (error) {
    console.error('❌ 获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

// 用户统计接口
app.get('/api/users/stats', async (req, res) => {
  try {
    console.log('📊 获取用户统计请求');

    const stats = await UserDao.getUserStats();

    const formattedStats = {
      total: stats.total,
      active: stats.active,
      inactive: stats.inactive,
      tourists: stats.tourists,
      guides: stats.guides,
      recentRegistrations: stats.recentRegistrations
    };

    console.log('📈 用户统计结果:', formattedStats);

    res.json({
      success: true,
      stats: formattedStats
    });

  } catch (error) {
    console.error('❌ 获取用户统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计失败'
    });
  }
});

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 初始化数据库并启动服务器
initializeDatabase().then(() => {
  app.listen(3000, () => {
    console.log('🚀 API server running at http://localhost:3000');
    console.log('📊 数据库已就绪');
  });
}).catch((error) => {
  console.error('❌ 数据库初始化失败:', error);
  process.exit(1);
});