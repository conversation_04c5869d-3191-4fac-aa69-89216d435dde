class SurveyStats {
  final Map<String, int> interest;
  final Map<String, int> language;
  final Map<String, int> identity;

  SurveyStats({required this.interest, required this.language, required this.identity});

  factory SurveyStats.fromJson(Map<String, dynamic> json) {
    return SurveyStats(
      interest: Map<String, int>.from(json['interest']),
      language: Map<String, int>.from(json['language']),
      identity: Map<String, int>.from(json['identity']),
    );
  }
} 