import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/locale_provider.dart';
import '../providers/auth_provider.dart';
import '../constants.dart';
import '../theme.dart';
import '../utils/performance_config.dart';
import '../widgets/primary_button.dart';
import 'ai_assistant_screen.dart';
import 'admin_dashboard_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final isChinese = localeProvider.locale == AppLocale.zh;

    return Scaffold(
      body: Stack(
        children: [
          CustomScrollView(
            slivers: [
              // 自定义AppBar
              SliverAppBar(
                expandedHeight: 200.0,
                floating: false,
                pinned: true,
                backgroundColor: AppColors.primary,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    isChinese ? '北京中轴线' : 'Beijing Central Axis',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppColors.primary,
                          AppColors.secondary,
                        ],
                      ),
                    ),
                    child: Stack(
                      children: [
                        Positioned(
                          top: -50,
                          right: -50,
                          child: Container(
                            width: 150,
                            height: 150,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: -30,
                          left: -30,
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.location_city,
                                size: 60,
                                color: Colors.white,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                isChinese ? '中秘文明互鉴' : 'Cultural Exchange',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w300,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: Icon(
                      localeProvider.locale == AppLocale.zh
                          ? Icons.language
                          : Icons.translate,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      localeProvider.toggleLocale();
                    },
                  ),
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, color: Colors.white),
                    onSelected: (value) {
                      switch (value) {
                        case 'logout':
                          authProvider.logout();
                          break;
                        case 'feedback':
                          Navigator.pushNamed(context, '/feedback');
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'feedback',
                        child: Row(
                          children: [
                            const Icon(Icons.feedback),
                            const SizedBox(width: 8),
                            Text(isChinese ? '意见反馈' : 'Feedback'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'logout',
                        child: Row(
                          children: [
                            const Icon(Icons.logout),
                            const SizedBox(width: 8),
                            Text(isChinese ? '退出登录' : 'Logout'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              // 主要内容
              SliverToBoxAdapter(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 欢迎信息
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [AppColors.accent, AppColors.secondary],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.accent.withOpacity(0.3),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  isChinese ? '欢迎回来！' : 'Welcome back!',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  isChinese 
                                    ? '探索北京中轴线的文化魅力'
                                    : 'Explore the cultural charm of Beijing Central Axis',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w300,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          const SizedBox(height: 24),
                          
                          // 角色区分
                          if (authProvider.isGuide) ...[
                            // 导游：显示管理功能区域
                            Text(
                              isChinese ? '导游管理面板' : 'Guide Management Panel',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            // 管理功能网格
                            GridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount: 2,
                              crossAxisSpacing: 12,
                              mainAxisSpacing: 12,
                              childAspectRatio: 1.2,
                              children: [
                                _buildManagementCard(
                                  context,
                                  icon: Icons.dashboard,
                                  title: isChinese ? '数据统计' : 'Statistics',
                                  subtitle: isChinese ? '查看调查数据' : 'View survey data',
                                  color: AppColors.primary,
                                  onTap: () => Navigator.push(
                                    context,
                                    MaterialPageRoute(builder: (_) => AdminDashboardScreen()),
                                  ),
                                ),
                                _buildManagementCard(
                                  context,
                                  icon: Icons.people,
                                  title: isChinese ? '用户管理' : 'User Management',
                                  subtitle: isChinese ? '管理游客账号' : 'Manage tourist accounts',
                                  color: AppColors.secondary,
                                  onTap: () => _showUserManagement(context, isChinese),
                                ),
                                _buildManagementCard(
                                  context,
                                  icon: Icons.content_paste,
                                  title: isChinese ? '内容管理' : 'Content Management',
                                  subtitle: isChinese ? '管理景点信息' : 'Manage spot info',
                                  color: AppColors.accent,
                                  onTap: () => _showContentManagement(context, isChinese),
                                ),
                                _buildManagementCard(
                                  context,
                                  icon: Icons.analytics,
                                  title: isChinese ? '反馈分析' : 'Feedback Analysis',
                                  subtitle: isChinese ? '查看用户反馈' : 'View user feedback',
                                  color: AppColors.success,
                                  onTap: () => _showFeedbackAnalysis(context, isChinese),
                                ),
                                _buildManagementCard(
                                  context,
                                  icon: Icons.photo_library,
                                  title: isChinese ? '照片管理' : 'Photo Management',
                                  subtitle: isChinese ? '管理景点照片' : 'Manage spot photos',
                                  color: AppColors.accent,
                                  onTap: () => _showPhotoManagement(context, isChinese),
                                ),
                                _buildManagementCard(
                                  context,
                                  icon: Icons.settings,
                                  title: isChinese ? '系统设置' : 'System Settings',
                                  subtitle: isChinese ? '应用配置' : 'App configuration',
                                  color: AppColors.warning,
                                  onTap: () => _showSystemSettings(context, isChinese),
                                ),
                                _buildManagementCard(
                                  context,
                                  icon: Icons.report,
                                  title: isChinese ? '生成报告' : 'Generate Report',
                                  subtitle: isChinese ? '导出数据报告' : 'Export data report',
                                  color: AppColors.info,
                                  onTap: () => _showReportGeneration(context, isChinese),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // 快速统计卡片
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    isChinese ? '今日概览' : 'Today Overview',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: _buildStatItem(
                                          Icons.person_add,
                                          '12',
                                          isChinese ? '新用户' : 'New Users',
                                          AppColors.primary,
                                        ),
                                      ),
                                      Expanded(
                                        child: _buildStatItem(
                                          Icons.quiz,
                                          '8',
                                          isChinese ? '新问卷' : 'New Surveys',
                                          AppColors.success,
                                        ),
                                      ),
                                      Expanded(
                                        child: _buildStatItem(
                                          Icons.feedback,
                                          '5',
                                          isChinese ? '新反馈' : 'New Feedback',
                                          AppColors.warning,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ] else ...[
                            // 游客：显示原有功能区
                            // 搜索导航条
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(25),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: InkWell(
                                onTap: () => Navigator.pushNamed(context, '/search'),
                                borderRadius: BorderRadius.circular(25),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.search,
                                      color: AppColors.textSecondary,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      isChinese ? '搜索景点、文化、美食...' : 'Search spots, culture, food...',
                                      style: TextStyle(
                                        color: AppColors.textSecondary,
                                        fontSize: 16,
                                      ),
                                    ),
                                    const Spacer(),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                      decoration: BoxDecoration(
                                        color: AppColors.primary.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(15),
                                      ),
                                      child: Text(
                                        isChinese ? '搜索' : 'Search',
                                        style: TextStyle(
                                          color: AppColors.primary,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // 功能模块网格
                            GridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount: 2,
                              crossAxisSpacing: 12,
                              mainAxisSpacing: 12,
                              childAspectRatio: 0.9,
                              children: [
                                _buildFeatureCard(
                                  context,
                                  icon: Icons.translate,
                                  title: isChinese ? '翻译助手' : 'Translation',
                                  color: AppColors.primary,
                                  onTap: () => Navigator.pushNamed(context, '/translation'),
                                ),
                                _buildFeatureCard(
                                  context,
                                  icon: Icons.map,
                                  title: isChinese ? '地图导航' : 'Map & Navigation',
                                  color: AppColors.secondary,
                                  onTap: () => Navigator.pushNamed(context, '/map'),
                                ),
                                _buildFeatureCard(
                                  context,
                                  icon: Icons.mic,
                                  title: isChinese ? '语音助手' : 'Voice Assistant',
                                  color: AppColors.accent,
                                  onTap: () => Navigator.pushNamed(context, '/voice'),
                                ),
                                _buildFeatureCard(
                                  context,
                                  icon: Icons.route,
                                  title: isChinese ? '行程规划' : 'Itinerary',
                                  color: AppColors.success,
                                  onTap: () => Navigator.pushNamed(context, '/itinerary'),
                                ),
                                _buildFeatureCard(
                                  context,
                                  icon: Icons.book,
                                  title: isChinese ? '文化手册' : 'Handbook',
                                  color: AppColors.warning,
                                  onTap: () => Navigator.pushNamed(context, '/handbook'),
                                ),
                                _buildFeatureCard(
                                  context,
                                  icon: Icons.quiz,
                                  title: isChinese ? '文化调查' : 'Survey',
                                  color: AppColors.info,
                                  onTap: () => Navigator.pushNamed(context, '/survey'),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // 快速访问
                            Text(
                              isChinese ? '快速访问' : 'Quick Access',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: [
                                  _buildQuickAccessCard(
                                    context,
                                    icon: Icons.photo_library,
                                    title: isChinese ? '照片墙' : 'Photo Wall',
                                    color: AppColors.primary,
                                    onTap: () => Navigator.pushNamed(context, '/photo'),
                                  ),
                                  const SizedBox(width: 12),
                                  _buildQuickAccessCard(
                                    context,
                                    icon: Icons.video_library,
                                    title: isChinese ? '视频' : 'Videos',
                                    color: AppColors.secondary,
                                    onTap: () => Navigator.pushNamed(context, '/video'),
                                  ),
                                  const SizedBox(width: 12),
                                  _buildQuickAccessCard(
                                    context,
                                    icon: Icons.animation,
                                    title: isChinese ? '动画' : 'Animation',
                                    color: AppColors.accent,
                                    onTap: () => Navigator.pushNamed(context, '/animation'),
                                  ),
                                  const SizedBox(width: 12),
                                  _buildQuickAccessCard(
                                    context,
                                    icon: Icons.museum,
                                    title: isChinese ? '文化' : 'Culture',
                                    color: AppColors.success,
                                    onTap: () => Navigator.pushNamed(context, '/culture'),
                                  ),
                                ],
                              ),
                            ),
                            
                            const SizedBox(height: 32),
                          ],
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          _DraggableAIBall(),
        ],
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 28,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickAccessCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return RepaintBoundary(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 110, // 进一步增加宽度
          constraints: const BoxConstraints(
            minHeight: 80,
            maxHeight: 100,
          ),
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  size: 16,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Flexible(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildManagementCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 28,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w300,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUserManagement(BuildContext context, bool isChinese) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: ListView(
                  controller: scrollController,
                  shrinkWrap: true,
                  children: [
                    Text(
                      isChinese ? '用户管理' : 'User Management',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    _buildUserCard('user', '<EMAIL>', '游客', isChinese),
                    _buildUserCard('admin', '<EMAIL>', '导游', isChinese),
                    _buildUserCard('guide1', '<EMAIL>', '导游', isChinese),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showContentManagement(BuildContext context, bool isChinese) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: ListView(
            controller: scrollController,
            padding: const EdgeInsets.all(20),
            children: [
              Text(
                isChinese ? '内容管理' : 'Content Management',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              _buildContentCard('故宫', 'Forbidden City', isChinese),
              _buildContentCard('天坛', 'Temple of Heaven', isChinese),
              _buildContentCard('前门', 'Qianmen', isChinese),
              _buildContentCard('钟鼓楼', 'Bell and Drum Towers', isChinese),
            ],
          ),
        ),
      ),
    );
  }

  void _showFeedbackAnalysis(BuildContext context, bool isChinese) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: ListView(
                  controller: scrollController,
                  shrinkWrap: true,
                  children: [
                    Text(
                      isChinese ? '反馈分析' : 'Feedback Analysis',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    _buildFeedbackCard('功能建议', '希望增加更多景点信息', 5, isChinese),
                    _buildFeedbackCard('界面优化', '界面很美观，用户体验很好', 5, isChinese),
                    _buildFeedbackCard('内容建议', '建议增加更多历史文化内容', 4, isChinese),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSystemSettings(BuildContext context, bool isChinese) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.8,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: ListView(
            controller: scrollController,
            padding: const EdgeInsets.all(20),
            children: [
              Text(
                isChinese ? '系统设置' : 'System Settings',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              _buildSettingItem(Icons.notifications, isChinese ? '通知设置' : 'Notifications', isChinese),
              _buildSettingItem(Icons.language, isChinese ? '语言设置' : 'Language', isChinese),
              _buildSettingItem(Icons.security, isChinese ? '安全设置' : 'Security', isChinese),
              _buildSettingItem(Icons.backup, isChinese ? '数据备份' : 'Data Backup', isChinese),
            ],
          ),
        ),
      ),
    );
  }

  void _showReportGeneration(BuildContext context, bool isChinese) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.5,
        minChildSize: 0.3,
        maxChildSize: 0.7,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: ListView(
                  controller: scrollController,
                  shrinkWrap: true,
                  children: [
                    Text(
                      isChinese ? '生成报告' : 'Generate Report',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    _buildReportOption('用户数据报告', 'User Data Report', isChinese),
                    _buildReportOption('调查问卷报告', 'Survey Report', isChinese),
                    _buildReportOption('反馈分析报告', 'Feedback Report', isChinese),
                    _buildReportOption('照片统计报告', 'Photo Report', isChinese),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPhotoManagement(BuildContext context, bool isChinese) {
    Navigator.pushNamed(context, '/photo_management');
  }

  void _showBatchUploadDialog(BuildContext context, bool isChinese) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isChinese ? '批量上传照片' : 'Batch Upload Photos'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(isChinese ? '选择要上传的照片：' : 'Select photos to upload:'),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.photo_camera),
                  label: Text(isChinese ? '拍照' : 'Camera'),
                  onPressed: () {
                    Navigator.pop(context);
                    _showUploadProgress(context, isChinese);
                  },
                ),
                ElevatedButton.icon(
                  icon: const Icon(Icons.photo_library),
                  label: Text(isChinese ? '相册' : 'Gallery'),
                  onPressed: () {
                    Navigator.pop(context);
                    _showUploadProgress(context, isChinese);
                  },
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(isChinese ? '取消' : 'Cancel'),
          ),
        ],
      ),
    );
  }

  void _showUploadProgress(BuildContext context, bool isChinese) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(isChinese ? '上传中...' : 'Uploading...'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const LinearProgressIndicator(),
            const SizedBox(height: 16),
            Text(isChinese ? '正在上传照片，请稍候...' : 'Uploading photos, please wait...'),
          ],
        ),
      ),
    );
    
    // 模拟上传进度
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isChinese ? '照片上传成功！' : 'Photos uploaded successfully!'),
          backgroundColor: AppColors.success,
        ),
      );
    });
  }

  Widget _buildPhotoStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoCategoryChip(String label, String labelEn, bool isSelected, bool isChinese) {
    return FilterChip(
      label: Text(isChinese ? label : labelEn),
      selected: isSelected,
      onSelected: (selected) {
        // 处理分类选择
      },
      selectedColor: AppColors.accent.withOpacity(0.2),
      checkmarkColor: AppColors.accent,
    );
  }

  Widget _buildPhotoItem(String title, String author, String date, bool isApproved, bool isChinese) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // 照片缩略图
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.accent.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.photo, color: AppColors.accent),
            ),
            
            const SizedBox(width: 12),
            
            // 照片信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    isChinese ? '上传者：$author' : 'Uploader: $author',
                    style: const TextStyle(fontSize: 12, color: AppColors.textSecondary),
                  ),
                  Text(
                    date,
                    style: const TextStyle(fontSize: 12, color: AppColors.textSecondary),
                  ),
                ],
              ),
            ),
            
            // 状态和操作按钮
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isApproved ? AppColors.success.withOpacity(0.1) : AppColors.warning.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isApproved ? (isChinese ? '已审核' : 'Approved') : (isChinese ? '待审核' : 'Pending'),
                    style: TextStyle(
                      fontSize: 10,
                      color: isApproved ? AppColors.success : AppColors.warning,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.visibility, size: 16),
                      onPressed: () {
                        // 预览照片
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit, size: 16),
                      onPressed: () {
                        // 编辑照片信息
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 16),
                      onPressed: () {
                        // 删除照片
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserCard(String username, String email, String role, bool isChinese) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: role == '导游' ? AppColors.primary : AppColors.secondary,
          child: Text(username[0].toUpperCase()),
        ),
        title: Text(username),
        subtitle: Text(email),
        trailing: Chip(
          label: Text(role),
          backgroundColor: role == '导游' ? AppColors.primary.withOpacity(0.1) : AppColors.secondary.withOpacity(0.1),
          labelStyle: TextStyle(
            color: role == '导游' ? AppColors.primary : AppColors.secondary,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  Widget _buildContentCard(String name, String nameEn, bool isChinese) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppColors.accent.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.location_on, color: AppColors.accent),
        ),
        title: Text(isChinese ? name : nameEn),
        subtitle: Text(isChinese ? '景点信息管理' : 'Spot Information Management'),
        trailing: const Icon(Icons.edit),
      ),
    );
  }

  Widget _buildFeedbackCard(String category, String content, int rating, bool isChinese) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  category,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Row(
                  children: List.generate(5, (index) => Icon(
                    index < rating ? Icons.star : Icons.star_border,
                    size: 16,
                    color: Colors.amber,
                  )),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(content),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(IconData icon, String title, bool isChinese) {
    return ListTile(
      leading: Icon(icon, color: AppColors.warning),
      title: Text(title),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
    );
  }

  Widget _buildReportOption(String title, String titleEn, bool isChinese) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.info.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.description, color: AppColors.info),
        ),
        title: Text(isChinese ? title : titleEn),
        subtitle: Text(isChinese ? '点击生成报告' : 'Click to generate report'),
        trailing: const Icon(Icons.download),
      ),
    );
  }

  Widget _buildStatItem(
    IconData icon,
    String value,
    String label,
    Color color,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 24,
          color: color,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w300,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}

// 在文件末尾添加可拖动悬浮球组件
class _DraggableAIBall extends StatefulWidget {
  @override
  State<_DraggableAIBall> createState() => _DraggableAIBallState();
}

class _DraggableAIBallState extends State<_DraggableAIBall> {
  Offset position = const Offset(0, 0);
  late double screenWidth;
  late double screenHeight;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final size = MediaQuery.of(context).size;
    screenWidth = size.width;
    screenHeight = size.height;
    if (position == Offset.zero) {
      // 初始放在右下角
      position = Offset(screenWidth - 80, screenHeight - 180);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: position.dx,
      top: position.dy,
      child: Draggable(
        feedback: _buildBall(),
        childWhenDragging: Container(),
        onDragEnd: (details) {
          setState(() {
            double x = details.offset.dx;
            double y = details.offset.dy;
            // 限制在屏幕范围内
            x = x.clamp(0, screenWidth - 60);
            y = y.clamp(0, screenHeight - 60);
            position = Offset(x, y);
          });
        },
        child: GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (context) => Dialog(
                insetPadding: const EdgeInsets.all(16),
                backgroundColor: Colors.transparent,
                child: SizedBox(
                  width: screenWidth * 0.95,
                  height: screenHeight * 0.8,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: Material(
                      child: AIAssistantScreen(),
                    ),
                  ),
                ),
              ),
            );
          },
          child: _buildBall(),
        ),
      ),
    );
  }

  Widget _buildBall() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
        ),
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: const Icon(
        Icons.smart_toy,
        color: Colors.white,
        size: 30,
      ),
    );
  }
} 