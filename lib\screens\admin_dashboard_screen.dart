import 'package:flutter/material.dart';
import '../services/admin_service.dart';
import '../models/survey_stats.dart';
import '../models/feedback_stats.dart';

class AdminDashboardScreen extends StatefulWidget {
  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  late Future<SurveyStats> surveyFuture;
  late Future<FeedbackStats> feedbackFuture;

  @override
  void initState() {
    super.initState();
    surveyFuture = AdminService.fetchSurveyStats();
    feedbackFuture = AdminService.fetchFeedbackStats();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('后台可视化统计'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          // 兴趣分布
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('兴趣分布', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 16),
                  FutureBuilder<SurveyStats>(
                    future: surveyFuture,
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) return Center(child: CircularProgressIndicator());
                      final data = snapshot.data!;
                      return Column(
                        children: data.interest.entries.map((e) => 
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 4),
                            child: Row(
                              children: [
                                Expanded(child: Text(e.key)),
                                Text('${e.value}人', style: TextStyle(fontWeight: FontWeight.bold)),
                              ],
                            ),
                          )
                        ).toList(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          
          SizedBox(height: 16),
          
          // 服务评分分布
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('服务评分分布', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 16),
                  FutureBuilder<FeedbackStats>(
                    future: feedbackFuture,
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) return Center(child: CircularProgressIndicator());
                      final data = snapshot.data!;
                      return Column(
                        children: data.ratings.entries.map((e) => 
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 4),
                            child: Row(
                              children: [
                                Text('${e.key}星'),
                                SizedBox(width: 8),
                                Expanded(
                                  child: LinearProgressIndicator(
                                    value: e.value / data.ratings.values.reduce((a, b) => a + b),
                                    backgroundColor: Colors.grey[300],
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                  ),
                                ),
                                SizedBox(width: 8),
                                Text('${e.value}人'),
                              ],
                            ),
                          )
                        ).toList(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          
          SizedBox(height: 16),
          
          // 最新评论
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('最新评论', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  SizedBox(height: 16),
                  FutureBuilder<FeedbackStats>(
                    future: feedbackFuture,
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) return Center(child: CircularProgressIndicator());
                      final data = snapshot.data!;
                      return Column(
                        children: data.comments.map((c) => ListTile(
                          leading: CircleAvatar(child: Text(c.user[0])),
                          title: Text('${c.user}（${c.score}星）'),
                          subtitle: Text(c.content),
                        )).toList(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 