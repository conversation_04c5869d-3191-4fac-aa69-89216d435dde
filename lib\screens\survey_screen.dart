import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import '../constants.dart';
import '../widgets/primary_button.dart';
import '../providers/locale_provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class SurveyScreen extends StatefulWidget {
  const SurveyScreen({Key? key}) : super(key: key);

  @override
  State<SurveyScreen> createState() => _SurveyScreenState();
}

class _SurveyScreenState extends State<SurveyScreen> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  List<String> interests = [];
  List<String> diets = [];
  String health = '';
  String expect = '';
  String suggestion = '';
  bool submitted = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<Map<String, dynamic>> interestOptions = [
    {'zh': '历史', 'en': 'History', 'icon': Icons.history_edu},
    {'zh': '建筑', 'en': 'Architecture', 'icon': Icons.architecture},
    {'zh': '美食', 'en': 'Food', 'icon': Icons.restaurant},
    {'zh': '自然', 'en': 'Nature', 'icon': Icons.nature},
    {'zh': '购物', 'en': 'Shopping', 'icon': Icons.shopping_bag},
    {'zh': '文化体验', 'en': 'Culture', 'icon': Icons.museum},
    {'zh': '其它', 'en': 'Other', 'icon': Icons.more_horiz},
  ];

  final List<Map<String, dynamic>> dietOptions = [
    {'zh': '无', 'en': 'None', 'icon': Icons.check_circle_outline},
    {'zh': '素食', 'en': 'Vegetarian', 'icon': Icons.eco},
    {'zh': '清真', 'en': 'Halal', 'icon': Icons.dining},
    {'zh': '海鲜过敏', 'en': 'Seafood Allergy', 'icon': Icons.warning_amber},
    {'zh': '坚果过敏', 'en': 'Nut Allergy', 'icon': Icons.warning_amber},
    {'zh': '其它', 'en': 'Other', 'icon': Icons.more_horiz},
  ];

  final List<Map<String, dynamic>> expectOptions = [
    {'zh': '历史文化', 'en': 'History & Culture', 'icon': Icons.account_balance},
    {'zh': '现代都市', 'en': 'Modern City', 'icon': Icons.location_city},
    {'zh': '美食', 'en': 'Food', 'icon': Icons.restaurant_menu},
    {'zh': '交友', 'en': 'Make Friends', 'icon': Icons.people},
    {'zh': '其它', 'en': 'Other', 'icon': Icons.more_horiz},
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadSurvey();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSurvey() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      interests = prefs.getStringList('survey_interests') ?? [];
      diets = prefs.getStringList('survey_diets') ?? [];
      health = prefs.getString('survey_health') ?? '';
      expect = prefs.getString('survey_expect') ?? '';
      suggestion = prefs.getString('survey_suggestion') ?? '';
      submitted = prefs.getBool('survey_submitted') ?? false;
    });
  }

  Future<void> _saveSurvey() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList('survey_interests', interests);
    await prefs.setStringList('survey_diets', diets);
    await prefs.setString('survey_health', health);
    await prefs.setString('survey_expect', expect);
    await prefs.setString('survey_suggestion', suggestion);
    await prefs.setBool('survey_submitted', true);
    _animationController.reset();
    setState(() {
      submitted = true;
    });
    _animationController.forward();

    final surveyData = {
      "interests": interests,
      "diets": diets,
      "health": health,
      "expect": expect,
      "suggestion": suggestion,
    };
    try {
      await http.post(
        Uri.parse('http://localhost:3000/api/survey/submit'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(surveyData),
      );
    } catch (e) {
      // 可选：处理网络异常
    }

    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isChinese = localeProvider.locale == AppLocale.zh;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(isChinese ? '提交成功！' : 'Submitted successfully!'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleProvider>(
      builder: (context, localeProvider, child) {
        final isChinese = localeProvider.locale == AppLocale.zh;
        
        return Scaffold(
          backgroundColor: Colors.grey.shade100,
          appBar: AppBar(
            title: Text(
              isChinese ? '行前需求调研' : 'Pre-trip Survey',
              style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 22),
            ),
            backgroundColor: Colors.white,
            foregroundColor: kPrimaryColor,
            actions: [
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(Icons.language, size: 20),
                  ),
                  tooltip: isChinese ? '切换到英文' : 'Switch to Chinese',
                  onPressed: localeProvider.toggleLocale,
                ),
              ),
            ],
          ),
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFE3F2FD),
                    Colors.white,
                  ],
                ),
              ),
              child: submitted ? _buildResult(isChinese) : _buildForm(isChinese),
            ),
          ),
        );
      },
    );
  }

  Widget _buildForm(bool isChinese) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildBanner(isChinese),
            const SizedBox(height: 20),
            _buildCard(_buildInterestsSection(isChinese)),
            const SizedBox(height: 16),
            _buildCard(_buildDietarySection(isChinese)),
            const SizedBox(height: 16),
            _buildCard(_buildHealthSection(isChinese)),
            const SizedBox(height: 16),
            _buildCard(_buildExpectationSection(isChinese)),
            const SizedBox(height: 16),
            _buildCard(_buildSuggestionSection(isChinese)),
            const SizedBox(height: 28),
            Center(
              child: PrimaryButton(
                text: isChinese ? '提交' : 'Submit',
                onPressed: _saveSurvey,
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildBanner(bool isChinese) {
    return Container(
      decoration: BoxDecoration(
        color: kPrimaryColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isChinese ? '北京中轴线中秘文明互鉴' : 'Beijing Central Axis Sino-Peru Civilization Exchange',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isChinese
                ? '请填写以下问卷，帮助我们更好地了解您的需求，为您定制专属行程体验。'
                : 'Please fill in the following survey to help us better understand your needs and customize your trip experience.',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 15,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCard(Widget child) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(14),
      ),
      child: Padding(
        padding: const EdgeInsets.all(18.0),
        child: child,
      ),
    );
  }

  Widget _buildInterestsSection(bool isChinese) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isChinese ? '您感兴趣的领域：' : 'Areas of Interest:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: interestOptions.map((option) {
            final optionText = isChinese ? option['zh'] : option['en'];
            final optionKey = option['en'];
            return ChoiceChip(
              label: Text(optionText),
              selected: interests.contains(optionKey),
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    interests.add(optionKey);
                  } else {
                    interests.remove(optionKey);
                  }
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDietarySection(bool isChinese) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isChinese ? '饮食偏好：' : 'Dietary Preferences:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: dietOptions.map((option) {
            final optionText = isChinese ? option['zh'] : option['en'];
            final optionKey = option['en'];
            return ChoiceChip(
              label: Text(optionText),
              selected: diets.contains(optionKey),
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    diets.add(optionKey);
                  } else {
                    diets.remove(optionKey);
                  }
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildHealthSection(bool isChinese) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isChinese ? '健康状况：' : 'Health Status:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        TextField(
          decoration: InputDecoration(
            labelText: isChinese ? '请描述您的健康状况' : 'Please describe your health status',
          ),
          onChanged: (value) {
            setState(() {
              health = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildExpectationSection(bool isChinese) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isChinese ? '旅行期望：' : 'Travel Expectations:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: expectOptions.map((option) {
            final optionText = isChinese ? option['zh'] : option['en'];
            final optionKey = option['en'];
            return ChoiceChip(
              label: Text(optionText),
              selected: expect == optionKey,
              onSelected: (selected) {
                setState(() {
                  expect = selected ? optionKey : '';
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSuggestionSection(bool isChinese) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isChinese ? '建议或意见：' : 'Suggestions or Comments:',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        TextField(
          decoration: InputDecoration(
            labelText: isChinese ? '请输入您的建议或意见' : 'Please enter your suggestions or comments',
          ),
          onChanged: (value) {
            setState(() {
              suggestion = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildResult(bool isChinese) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.emoji_events, color: kPrimaryColor, size: 48),
          const SizedBox(height: 20),
          Text(
            isChinese ? '感谢您的参与！' : 'Thank you for your participation!',
            style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Text(
            isChinese ? '我们将根据您的反馈，为您提供更好的服务。' : 'We will use your feedback to provide you with better service.',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}