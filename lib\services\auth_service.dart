import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class AuthService {
  static const String _userKey = 'user';
  static const String _tokenKey = 'token';
  static const String _rememberMeKey = 'rememberMe';

  // 模拟用户数据 - 包含角色信息
  static final List<Map<String, dynamic>> _mockUsers = [
    {
      'id': '1',
      'username': 'admin',
      'email': '<EMAIL>',
      'password': '123456',
      'avatar': null,
      'createdAt': DateTime.now().toIso8601String(),
      'isActive': true,
      'role': 'guide',  // 管理员是导游角色
    },
    {
      'id': '2',
      'username': 'user',
      'email': '<EMAIL>',
      'password': '123456',
      'avatar': null,
      'createdAt': DateTime.now().toIso8601String(),
      'isActive': true,
      'role': 'tourist',  // 普通用户是游客角色
    },
    {
      'id': '3',
      'username': 'guide1',
      'email': '<EMAIL>',
      'password': '123456',
      'avatar': null,
      'createdAt': DateTime.now().toIso8601String(),
      'isActive': true,
      'role': 'guide',  // 导游账号
    },
  ];

  // 登录
  Future<User?> login(String username, String password, {bool rememberMe = false}) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 200));

    // 查找用户
    final userData = _mockUsers.firstWhere(
      (user) => user['username'] == username && user['password'] == password,
      orElse: () => throw Exception('用户名或密码错误'),
    );

    final user = User.fromJson(userData);
    
    // 保存用户信息
    await _saveUser(user);
    await _saveRememberMe(rememberMe);
    
    return user;
  }

  // 注册 - 支持指定角色
  Future<User> register(String username, String email, String password, {UserRole role = UserRole.tourist}) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 200));

    // 检查用户名是否已存在
    if (_mockUsers.any((user) => user['username'] == username)) {
      throw Exception('用户名已存在');
    }

    // 检查邮箱是否已存在
    if (_mockUsers.any((user) => user['email'] == email)) {
      throw Exception('邮箱已存在');
    }

    final newUser = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'username': username,
      'email': email,
      'password': password,
      'avatar': null,
      'createdAt': DateTime.now().toIso8601String(),
      'isActive': true,
      'role': role.toString().split('.').last,
    };

    _mockUsers.add(newUser);
    
    final user = User.fromJson(newUser);
    await _saveUser(user);
    
    return user;
  }

  // 登出
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_tokenKey);
  }

  // 获取当前用户
  Future<User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(_userKey);
    if (userJson != null) {
      return User.fromJson(jsonDecode(userJson));
    }
    return null;
  }

  // 检查是否已登录
  Future<bool> isLoggedIn() async {
    final user = await getCurrentUser();
    return user != null;
  }

  // 获取记住我状态
  Future<bool> getRememberMe() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberMeKey) ?? false;
  }

  // 保存用户信息
  Future<void> _saveUser(User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
  }

  // 保存记住我状态
  Future<void> _saveRememberMe(bool rememberMe) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_rememberMeKey, rememberMe);
  }

  // 更新用户信息
  Future<void> updateUser(User user) async {
    await _saveUser(user);
  }
} 