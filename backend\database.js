const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, 'travel_app.db');

// 创建数据库连接
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
  } else {
    console.log('✅ 数据库连接成功:', dbPath);
  }
});

// 初始化数据库表
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    // 创建用户表
    const createUsersTable = `
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        avatar TEXT,
        role TEXT NOT NULL DEFAULT 'tourist',
        is_active BOOLEAN NOT NULL DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // 创建问卷提交表
    const createSurveysTable = `
      CREATE TABLE IF NOT EXISTS survey_submissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        interests TEXT,
        diets TEXT,
        health TEXT,
        expect TEXT,
        gender TEXT,
        age_group TEXT,
        monthly_income TEXT,
        cultural_identity TEXT,
        psychological_traits TEXT,
        travel_frequency TEXT,
        suggestion TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `;

    // 创建照片表
    const createPhotosTable = `
      CREATE TABLE IF NOT EXISTS photos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        filename TEXT NOT NULL,
        original_name TEXT NOT NULL,
        spot_name TEXT NOT NULL,
        title TEXT,
        description TEXT,
        uploader TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        approved_at DATETIME,
        approved_by INTEGER,
        FOREIGN KEY (approved_by) REFERENCES users (id)
      )
    `;

    // 创建反馈表
    const createFeedbackTable = `
      CREATE TABLE IF NOT EXISTS feedback (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        rating INTEGER NOT NULL,
        comment TEXT,
        category TEXT,
        submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    `;

    // 执行表创建
    db.serialize(() => {
      db.run(createUsersTable, (err) => {
        if (err) {
          console.error('❌ 创建用户表失败:', err.message);
          reject(err);
          return;
        }
        console.log('✅ 用户表创建成功');
      });

      db.run(createSurveysTable, (err) => {
        if (err) {
          console.error('❌ 创建问卷表失败:', err.message);
          reject(err);
          return;
        }
        console.log('✅ 问卷表创建成功');
      });

      db.run(createPhotosTable, (err) => {
        if (err) {
          console.error('❌ 创建照片表失败:', err.message);
          reject(err);
          return;
        }
        console.log('✅ 照片表创建成功');
      });

      db.run(createFeedbackTable, (err) => {
        if (err) {
          console.error('❌ 创建反馈表失败:', err.message);
          reject(err);
          return;
        }
        console.log('✅ 反馈表创建成功');
        
        // 插入初始用户数据
        insertInitialData().then(() => {
          console.log('🎉 数据库初始化完成');
          resolve();
        }).catch(reject);
      });
    });
  });
}

// 插入初始数据
function insertInitialData() {
  return new Promise((resolve, reject) => {
    // 检查是否已有用户数据
    db.get("SELECT COUNT(*) as count FROM users", (err, row) => {
      if (err) {
        reject(err);
        return;
      }

      if (row.count > 0) {
        console.log('📊 数据库已有数据，跳过初始化');
        resolve();
        return;
      }

      // 插入初始用户
      const insertUsers = `
        INSERT INTO users (username, email, password, role, created_at) VALUES
        ('admin', '<EMAIL>', '123456', 'guide', '2024-01-01 00:00:00'),
        ('user', '<EMAIL>', '123456', 'tourist', '2024-01-15 00:00:00'),
        ('guide1', '<EMAIL>', '123456', 'guide', '2024-02-01 00:00:00')
      `;

      db.run(insertUsers, (err) => {
        if (err) {
          console.error('❌ 插入初始用户失败:', err.message);
          reject(err);
          return;
        }
        console.log('✅ 初始用户数据插入成功');
        resolve();
      });
    });
  });
}

// 导出数据库实例和初始化函数
module.exports = {
  db,
  initializeDatabase
};
