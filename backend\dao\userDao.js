const { db } = require('../database');

class UserDao {
  // 创建用户
  static createUser(userData) {
    return new Promise((resolve, reject) => {
      const { username, email, password, role = 'tourist' } = userData;
      const sql = `
        INSERT INTO users (username, email, password, role)
        VALUES (?, ?, ?, ?)
      `;
      
      db.run(sql, [username, email, password, role], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        // 返回新创建的用户信息
        UserDao.getUserById(this.lastID).then(resolve).catch(reject);
      });
    });
  }

  // 根据用户名和密码查找用户
  static findUserByCredentials(username, password) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT id, username, email, avatar, role, is_active, created_at, updated_at
        FROM users 
        WHERE username = ? AND password = ?
      `;
      
      db.get(sql, [username, password], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(row);
      });
    });
  }

  // 根据ID获取用户
  static getUserById(id) {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT id, username, email, avatar, role, is_active, created_at, updated_at
        FROM users 
        WHERE id = ?
      `;
      
      db.get(sql, [id], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(row);
      });
    });
  }

  // 检查用户名是否存在
  static checkUsernameExists(username) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) as count FROM users WHERE username = ?`;
      
      db.get(sql, [username], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(row.count > 0);
      });
    });
  }

  // 检查邮箱是否存在
  static checkEmailExists(email) {
    return new Promise((resolve, reject) => {
      const sql = `SELECT COUNT(*) as count FROM users WHERE email = ?`;
      
      db.get(sql, [email], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(row.count > 0);
      });
    });
  }

  // 获取所有用户
  static getAllUsers() {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT id, username, email, avatar, role, is_active, created_at, updated_at
        FROM users 
        ORDER BY created_at DESC
      `;
      
      db.all(sql, [], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(rows);
      });
    });
  }

  // 获取用户统计
  static getUserStats() {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
          SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive,
          SUM(CASE WHEN role = 'tourist' THEN 1 ELSE 0 END) as tourists,
          SUM(CASE WHEN role = 'guide' THEN 1 ELSE 0 END) as guides,
          SUM(CASE WHEN created_at > datetime('now', '-7 days') THEN 1 ELSE 0 END) as recentRegistrations
        FROM users
      `;
      
      db.get(sql, [], (err, row) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(row);
      });
    });
  }

  // 更新用户信息
  static updateUser(id, userData) {
    return new Promise((resolve, reject) => {
      const { username, email, avatar, role, is_active } = userData;
      const sql = `
        UPDATE users 
        SET username = ?, email = ?, avatar = ?, role = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      db.run(sql, [username, email, avatar, role, is_active, id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        
        UserDao.getUserById(id).then(resolve).catch(reject);
      });
    });
  }

  // 删除用户
  static deleteUser(id) {
    return new Promise((resolve, reject) => {
      const sql = `DELETE FROM users WHERE id = ?`;
      
      db.run(sql, [id], function(err) {
        if (err) {
          reject(err);
          return;
        }
        resolve({ deletedRows: this.changes });
      });
    });
  }
}

module.exports = UserDao;
